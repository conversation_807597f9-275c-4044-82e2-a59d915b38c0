## Technical Requirements Document (TRD) - Agent Platform Service

**1. Introduction & Overview**

This document outlines the technical requirements for a Python service (`agent-platform`) designed to dynamically create and manage AI agent sessions. The service provides a FastAPI interface for client interactions and uses Kafka for asynchronous message processing. Agent configurations are managed using Redis, and the platform supports dynamic tool loading and streaming responses.

**2. Goals**

- Provide a modular backend system for marketing content generation and AI task delegation
- Support dynamic agent configuration and instantiation based on user requirements
- Implement a flexible tool loading mechanism for extending agent capabilities
- Enable real-time streaming of agent responses using Server-Sent Events (SSE)
- Facilitate agent-to-agent communication through a structured messaging system
- Maintain conversation state across user interactions using Redis
- Support integration with external workflow systems and MCPs (Machine Control Programs)
- Handle errors gracefully and provide meaningful feedback
- Implement comprehensive logging throughout the application
- Support knowledge retrieval from various sources (documents, websites, text)

**3. Scope**

- **In Scope:**
  - FastAPI application for client interactions
  - Kafka integration for asynchronous message processing
  - Redis integration for session management
  - Dynamic agent configuration and instantiation
  - Tool loading and execution framework
  - Streaming response handling
  - Comprehensive error handling and logging
  - Configuration management
  - Docker containerization
  - Workflow tool integration
  - MCP tool integration
  - Dynamic API tool creation
  - Knowledge management and retrieval (RAG capabilities)
- **Out of Scope:**
  - User interface implementation
  - Authentication and authorization (beyond basic token validation)
  - Advanced monitoring and alerting
  - Deployment infrastructure provisioning
  - Complex workflow orchestration (delegated to external systems)

**4. High-Level Architecture**

```mermaid
graph TB
    Client[Client Application] -->|HTTP/SSE| API[FastAPI Server]
    API -->|Session Management| SM[Session Manager]
    API -->|Task Routing| HA[Head Agent]

    subgraph "Agent Layer"
        HA -->|Delegates| MA[Marketing Agent]
        HA -->|Delegates| SA[Sales Agent]
        HA -->|Delegates| CA[Content Agent]

        MA & SA & CA -->|Uses| TL[Tool Loader]
        MA & SA & CA -->|Retrieves| KM[Knowledge Manager]
    end

    subgraph "Tool Layer"
        TL -->|Loads| MT[MCP Tools]
        TL -->|Loads| WF[Workflow Tools]
        TL -->|Loads| FT[Function Tools]
    end

    subgraph "Knowledge Layer"
        KM -->|Indexes| DOC[Documents]
        KM -->|Scrapes| WEB[Websites]
        KM -->|Stores| TXT[Text Input]
        KM -->|Uses| VDB[Vector Database]
    end

    subgraph "External Services"
        MT -->|Connects| MCP[MCP Service]
        WF -->|Executes| WFA[Workflow API]
        FT -->|Calls| EXT[External APIs]
        VDB -->|Stores| CHROMA[ChromaDB]
    end
```

**5. Functional Requirements**

- **FR1: Agent Configuration and Registration**

  - Support dynamic agent configuration via JSON
  - Register agents with the runtime system
  - Configure agent parameters (system message, tools, model)
  - Support different agent types (Head Agent, specialized agents)

- **FR2: Message Routing and Processing**

  - Route user messages to appropriate agents
  - Support delegation between agents
  - Process messages using LLM models
  - Return responses to users

- **FR3: Tool Integration**

  - Load tools from JSON configurations
  - Support different tool types (MCP, Workflow, Function, API)
  - Execute tools and process results
  - Stream tool execution results when appropriate
  - Provide comprehensive logging for tool execution

- **FR4: Session Management**

  - Create and maintain user sessions
  - Store compressed conversation history in Redis
  - Implement message window management with configurable limits
  - Store only essential agent configuration data
  - Use binary storage for efficient data handling
  - Retrieve session data for context in conversations
  - Implement session expiration and cleanup
  - Support efficient session listing with Redis SCAN

- **FR5: Streaming Responses**

  - Support Server-Sent Events for real-time responses
  - Stream partial responses as they become available
  - Handle connection interruptions gracefully

- **FR6: Kafka Integration**

  - Consume messages from Kafka topics
  - Process messages asynchronously
  - Publish responses to Kafka topics
  - Support correlation IDs for request/response matching

- **FR7: Configuration Management**

  - Load configuration from environment variables
  - Support different environments (development, production)
  - Secure handling of API keys and secrets
  - Allow runtime configuration updates

- **FR8: Logging System**

  - Implement structured logging throughout the application
  - Support different log levels based on environment
  - Include contextual information in logs
  - Provide clear error messages for troubleshooting

- **FR9: Knowledge Management**
  - Support document uploads for knowledge extraction
  - Enable website scraping for knowledge acquisition
  - Allow direct text input as knowledge sources
  - Implement vector-based retrieval for relevant information
  - Enhance agent responses with retrieved knowledge

**6. Non-Functional Requirements**

- **NFR1: Performance**

  - Response time for chat requests should be minimized (target < 100ms excluding LLM inference)
  - Support concurrent user sessions (minimum 100 concurrent sessions)
  - Efficient message processing and routing (throughput of 1000+ messages per minute)
  - Optimize knowledge retrieval for minimal latency (< 50ms for vector search)
  - Streaming response latency under 200ms for first token
  - Reduced storage usage through message compression (50-80% reduction)
  - Efficient memory usage with message window management
  - Fast session retrieval with optimized data structures

- **NFR2: Scalability**

  - Stateless design to allow horizontal scaling
  - Redis for shared state across instances
  - Kafka for distributed message processing
  - Efficient vector storage for knowledge management
  - Support for containerized deployment and orchestration
  - Ability to scale components independently based on load

- **NFR3: Reliability**

  - Robust error handling for external dependencies
  - Graceful degradation when services are unavailable
  - Comprehensive logging for troubleshooting
  - Fallback mechanisms for knowledge retrieval

- **NFR4: Security**

  - Secure handling of API keys and tokens
  - Input validation to prevent injection attacks
  - Secure connections to external services
  - Proper sanitization of knowledge sources

- **NFR5: Maintainability**
  - Modular code structure
  - Comprehensive documentation
  - Unit and integration tests
  - Clear separation of concerns

**7. Technology Stack**

- **Language:** Python 3.12+
- **Web Framework:** FastAPI
- **Message Queue:** Kafka (aiokafka)
- **Session Store:** Redis
- **LLM Integration:** OpenAI API, Anthropic API
- **Agent Framework:** autogen-core, autogen-ext
- **Data Validation:** Pydantic
- **Configuration:** python-dotenv
- **Containerization:** Docker
- **Testing:** pytest, pytest-asyncio, pytest-cov
- **HTTP Client:** httpx, aiohttp, requests
- **Vector Database:** ChromaDB
- **File Processing:** aiofiles
- **Monitoring:** Prometheus, Grafana
- **Logging:** Structured JSON logging
- **Security:** TLS, API key authentication
- **Documentation:** Swagger UI, ReDoc

**8. Data Flow**

1. **User Interaction Flow**

   - User sends message to FastAPI endpoint
   - Message is published to Kafka topic
   - Head Agent consumes message and analyzes it
   - Task is delegated to specialized agent if needed
   - Agent processes message and generates response
   - Response is streamed back to user via SSE

2. **Tool Execution Flow**

   - Agent identifies need for tool execution
   - Tool loader creates appropriate tool instance
   - Tool executes against external service
   - Results are processed and returned to agent
   - Agent incorporates results into response
   - Comprehensive logging tracks the entire process

3. **Session Management Flow**

   - User session is created or retrieved
   - Only essential agent configuration is stored
   - Conversation history is compressed and stored in Redis
   - Messages are truncated to maintain a configurable window
   - Context is provided to agents for processing
   - Session is updated with new messages
   - Binary storage is used for efficient data handling
   - Session expires after configured timeout
   - Efficient session listing with Redis SCAN

4. **Knowledge Retrieval Flow**
   - Knowledge sources are processed and indexed
   - Text is chunked and stored in vector database
   - Agent queries are used to retrieve relevant knowledge
   - Retrieved knowledge is incorporated into agent context
   - Agent generates responses enhanced with knowledge

**9. API Endpoints**

1. **Chat Endpoint**

   - `POST /chat`: Process user message and return response
   - `POST /chat/stream`: Process user message and stream response via SSE

2. **Session Management**

   - `POST /sessions`: Create new session
   - `GET /sessions/{session_id}`: Get session details
   - `DELETE /sessions/{session_id}`: End session

3. **Agent Management**

   - `POST /agents`: Register new agent
   - `GET /agents`: List available agents
   - `GET /agents/{agent_id}`: Get agent details

4. **Knowledge Management**

   - `POST /knowledge/add/{agent_id}`: Add knowledge to an agent
   - `DELETE /knowledge/{agent_id}`: Clear knowledge for an agent
   - `GET /knowledge/{agent_id}/status`: Get knowledge status

5. **System Management**
   - `GET /health`: Health check endpoint
   - `POST /reload-config`: Reload configuration

**10. Logging Requirements**

- **Log Levels**

  - DEBUG: Detailed information for debugging
  - INFO: General information about system operation
  - WARNING: Potential issues that don't affect operation
  - ERROR: Errors that affect specific operations
  - CRITICAL: Critical errors that affect the entire system

- **Log Context**

  - Request ID for tracing
  - User/Session ID for user context
  - Component name for source identification
  - Timestamp for temporal tracking
  - Environment information

- **Log Format**
  - Structured JSON format for machine parsing
  - Human-readable format for development

**11. Future Enhancements**

- Integration with additional LLM providers
- Support for more complex agent architectures
- Enhanced monitoring and observability
- Advanced security features
- Performance optimizations for large-scale deployments
- Advanced tool discovery and registration
- Improved error recovery mechanisms
- Multi-modal knowledge sources (images, audio)
- Fine-tuning capabilities for domain-specific knowledge
- Distributed agent deployment across multiple nodes
- Advanced caching mechanisms for improved performance
- Custom model hosting integration
- Automated agent testing framework
- Agent performance analytics dashboard
- Hybrid on-premise/cloud deployment options
- Advanced rate limiting and quota management

---

## Coding Task List

**Phase 1: Setup & Core Components**

1.  **[Setup]** Initialize Python project (e.g., using Poetry or `venv`).
2.  **[Setup]** Set up Git repository and `.gitignore`.
3.  **[Setup]** Define project file structure (see below).
4.  **[Config]** Implement configuration loading (e.g., using `python-dotenv` for local dev, environment variables for deployment). Include Kafka brokers, topics, Redis connection details, API endpoint, API secret key. **Ensure API key is not committed.**
5.  **[Logging]** Set up structured logging (e.g., `logging` module configured for JSON output).
6.  **[Models]** Define Pydantic models for Kafka message schemas (creation, chat request, chat response) and the expected API response structure.
7.  **[Kafka]** Implement Kafka Producer wrapper/utility class (`kafka_client/producer.py`).
8.  **[Kafka]** Implement Kafka Consumer wrapper/utility class (`kafka_client/consumer.py`) - capable of handling multiple topics/callbacks.
9.  **[Redis]** Implement Redis client wrapper/utility class (`helper/redis_client.py`) - connect, set hash, get hash, check existence, binary data storage, and efficient key scanning.
10. **[API]** Implement External API client class (`api_client/client.py`) - function to fetch agent config by `agent_id`, handling auth (secret key) and errors.

**Phase 2: Agent Creation Flow**

11. **[Agent]** Create `AgentFactory` (`autogen_service/agent_factory.py`) responsible for taking parsed config data and returning an instantiated AutoGen agent.
12. **[Agent]** Implement basic agent creation in `AgentFactory` (e.g., `ConversableAgent` with name, system message, llm_config).
13. **[Agent]** Implement logic to add predefined tools to the agent based on config (Tool mapping/registry might be needed).
14. **[Session]** Implement `SessionManager` (`autogen_service/session_manager.py`) using the Redis client to create sessions (generate ID, store config).
    14a. **[Session]** Implement memory compression in SessionManager using zlib.
    14b. **[Session]** Add message window management with configurable limits.
    14c. **[Session]** Implement selective agent configuration storage.
    14d. **[Session]** Add binary storage for efficient data handling.
    14e. **[Session]** Implement efficient session listing with Redis SCAN.
15. **[Kafka]** Implement the consumer logic for the `agent_creation_requests` topic:
    - Deserialize and validate message using Pydantic model.
    - Call API client to fetch config.
    - Call `SessionManager` to create and store the session.
    - Log success/failure. (No response needed back on Kafka for creation in this design).
16. **[Main]** Integrate the creation consumer into the main application entry point (`main.py` or `app.py`).

**Phase 3: Chat Flow**

17. **[Session]** Add method to `SessionManager` to retrieve session config by `session_id`.
18. **[Agent]** Create `ChatProcessor` (`autogen_service/chat_processor.py`) orchestrating the chat flow.
19. **[Chat]** Implement logic in `ChatProcessor`:
    - Takes `session_id` and `user_message`.
    - Calls `SessionManager` to get config.
    - Calls `AgentFactory` to re-instantiate the agent.
    - Initiates chat with the agent (`agent.initiate_chat` or similar, ensuring only one turn if needed).
    - Extracts the agent's response.
    - Returns the response or raises exceptions on failure.
20. **[Kafka]** Implement the consumer logic for the `agent_chat_requests` topic:
    - Deserialize and validate message.
    - Call `ChatProcessor` to get the agent response.
    - Construct the response message (success or error).
    - Call Kafka Producer to send the response to `agent_chat_responses`.
    - Log success/failure/errors.
21. **[Main]** Integrate the chat consumer into the main application entry point.

**Phase 4: Finalization & Deployment**

22. **[Error Handling]** Implement robust error handling around Kafka, Redis, API calls, and agent execution throughout the service. Define retry logic where appropriate.
23. **[Testing]** Write unit tests for key components (config loading, clients, factory, session manager, message validation). Mock external dependencies.
24. **[Testing]** (Optional) Write basic integration tests (e.g., testing the flow with mocked Kafka/Redis/API).
25. **[Docs]** Write `README.md` with setup instructions, configuration details, and how to run the service.
26. **[Docker]** Create `Dockerfile` to containerize the service.
27. **[Docker]** Create `docker-compose.yml` (optional) for easy local testing with Kafka/Redis containers.
28. **[Refactor]** Code review, cleanup, and final refactoring.

---
