# Google Drive Knowledge Tools Implementation

This document describes the implementation of custom Google Drive knowledge content retrieval tools using AutoGen's `FunctionTool` pattern.

## Overview

Two new Google Drive knowledge content retrieval functions have been implemented as AutoGen tools:

1. **`get_knowledge_content`** - Search and retrieve knowledge content from Google Drive based on a query
2. **`get_batch_knowledge_content`** - Perform batch search across multiple queries in Google Drive

These tools are implemented following AutoGen's best practices for custom function tools, using the `FunctionTool` class with proper type annotations and descriptions. They integrate with Google Drive API endpoints for knowledge retrieval.

## Implementation Details

### 1. Google Drive Knowledge Search Tool

**Function**: `get_knowledge_content`

**Purpose**: Search through the organization's Google Drive knowledge base and return relevant content that matches a query.

**Parameters**:

- `query` (str): Search query to find relevant knowledge content
- `user_id` (str): User ID for the search request
- `agent_id` (str): Agent ID for the search request
- `top_k` (int, optional): Maximum number of results to return (default: 5)

**Returns**: Formatted string with search results including titles, file IDs, sources, relevance scores, and content previews.

**Example Usage**:

```python
result = await get_knowledge_content(
    query="How to setup authentication",
    user_id="user-123",
    agent_id="agent-456",
    top_k=3
)
```

### 2. Google Drive Batch Search Tool

**Function**: `get_batch_knowledge_content`

**Purpose**: Perform batch search across multiple queries in Google Drive knowledge base for efficient multi-topic searches.

**Parameters**:

- `query_texts` (List[str]): List of search queries to find relevant knowledge content
- `user_id` (str): User ID for the search request
- `agent_id` (str): Agent ID for the search request
- `top_k` (int, optional): Maximum number of results to return per query (default: 5)

**Returns**: Formatted string with batch search results organized by query, including titles, file IDs, sources, relevance scores, and content previews.

**Example Usage**:

```python
result = await get_batch_knowledge_content(
    query_texts=[
        "authentication setup procedures",
        "user management best practices",
        "security configuration guide"
    ],
    user_id="user-123",
    agent_id="agent-456",
    top_k=2
)
```

## Integration with Agent Factory

The knowledge tools are automatically integrated into the agent creation process when:

1. `use_knowledge` is set to `True` in the `AgentCreationRequest`
2. An `organization_id` is provided

The `_load_knowledge_tools` method in `AgentFactory` now:

1. **Always adds knowledge content tools** when knowledge is enabled
2. **Loads dynamic knowledge tools** from organization-specific knowledge sources
3. **Combines both types** of tools for comprehensive knowledge access

## Code Structure

### KnowledgeToolLoader Class

**New Method**: `create_knowledge_content_tools(organization_id: Optional[str] = None) -> List[FunctionTool]`

This method:

- Creates two `FunctionTool` instances for the knowledge functions
- Configures them with proper descriptions and type annotations
- Returns a list of tools ready to be used by agents

### Agent Factory Integration

**Enhanced Method**: `_load_knowledge_tools`

The method now:

```python
# Always add the knowledge content retrieval tools
content_tools = knowledge_loader.create_knowledge_content_tools(
    organization_id=organization_id
)
all_tools.extend(content_tools)

# Load dynamic knowledge tools from sources (if available)
if knowledge_sources:
    dynamic_tools = await knowledge_loader.load_knowledges_as_tools(
        knowledge_sources
    )
    all_tools.extend(dynamic_tools)
```

## API Endpoints

The tools make HTTP requests to the following Google Drive API endpoints:

1. **Google Drive Search**: `POST /api/v1/google-drive/search`

   - Payload: `{"user_id": str, "query_text": str, "top_k": int, "agent_id": str, "organisation_id": str}`
   - Response: `{"results": [{"title": str, "content": str, "source": str, "file_id": str, "score": float, ...}]}`

2. **Google Drive Batch Search**: `POST /api/v1/google-drive/batch-search`
   - Payload: `{"user_id": str, "query_texts": [str], "top_k": int, "agent_id": str, "organisation_id": str}`
   - Response: `{"results": [{"results": [{"title": str, "content": str, "source": str, "file_id": str, "score": float, ...}]}]}`

## Error Handling

Both tools include comprehensive error handling:

- **Network errors**: Gracefully handled with informative error messages
- **Invalid responses**: Checked for expected response format
- **Missing data**: Appropriate fallback messages when no content is found
- **Logging**: Detailed logging for debugging and monitoring

## Usage Examples

### Direct Tool Usage

```python
from app.tools.knowledge_tool_loader import KnowledgeToolLoader

# Create tools
loader = KnowledgeToolLoader()
tools = loader.create_knowledge_content_tools(organization_id="org-123")

# Use tools
cancellation_token = CancellationToken()
result = await tools[0].run_json(
    {"query": "authentication setup", "limit": 5},
    cancellation_token
)
```

### Agent Integration

```python
from autogen_agentchat.agents import AssistantAgent

# Tools are automatically loaded when creating agents with knowledge enabled
agent = AssistantAgent(
    name="knowledge_assistant",
    model_client=model_client,
    tools=knowledge_tools,  # Includes both search and detail tools
    system_message="You have access to knowledge tools. Use them to help users.",
)
```

## Benefits

1. **Standardized Interface**: Uses AutoGen's `FunctionTool` pattern for consistency
2. **Type Safety**: Proper type annotations ensure correct usage
3. **Automatic Integration**: Seamlessly integrated into the agent creation pipeline
4. **Flexible Search**: Supports various search parameters and filters
5. **Detailed Retrieval**: Provides complete information for specific knowledge items
6. **Error Resilience**: Robust error handling for production use
7. **Logging**: Comprehensive logging for monitoring and debugging

## Testing

A test script is provided at `app/tools/test_knowledge_tools.py` that demonstrates:

1. Direct tool usage and testing
2. Agent integration with knowledge tools
3. Error handling scenarios
4. Tool schema validation

Run the test with:

```bash
cd app/tools
python test_knowledge_tools.py
```

## Future Enhancements

Potential improvements for the knowledge tools:

1. **Caching**: Implement response caching for frequently accessed knowledge
2. **Pagination**: Support for paginated search results
3. **Advanced Filtering**: More sophisticated search filters and sorting
4. **Semantic Search**: Integration with vector databases for semantic search
5. **Real-time Updates**: Support for real-time knowledge base updates
6. **Analytics**: Usage analytics and search performance metrics
